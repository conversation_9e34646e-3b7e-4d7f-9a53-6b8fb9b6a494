/* Custom Modal Styles */
.custom-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(5px);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.custom-modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.custom-modal {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    transform: scale(0.8) translateY(50px);
    transition: all 0.3s ease;
    position: relative;
}

.custom-modal-overlay.show .custom-modal {
    transform: scale(1) translateY(0);
}

.custom-modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px 30px;
    text-align: center;
    position: relative;
}

.custom-modal-header h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.custom-modal-header .icon {
    font-size: 3rem;
    margin-bottom: 10px;
    opacity: 0.9;
}

.custom-modal-close {
    position: absolute;
    top: 15px;
    right: 20px;
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    opacity: 0.8;
    transition: opacity 0.2s ease;
}

.custom-modal-close:hover {
    opacity: 1;
}

.custom-modal-body {
    padding: 30px;
    text-align: center;
}

.custom-modal-body p {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 30px;
    line-height: 1.6;
}

.custom-modal-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.custom-modal-btn {
    padding: 12px 30px;
    border: none;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.custom-modal-btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.custom-modal-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

.custom-modal-btn-secondary {
    background: #f8f9fa;
    color: #6c757d;
    border: 2px solid #e9ecef;
}

.custom-modal-btn-secondary:hover {
    background: #e9ecef;
    transform: translateY(-1px);
}

.custom-modal-btn-danger {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

.custom-modal-btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
}

/* Confirmation Modal Specific Styles */
.confirm-modal .custom-modal-header {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
}

.confirm-modal .custom-modal-header .icon {
    color: #fff;
}

/* Success Modal Specific Styles */
.success-modal .custom-modal-header {
    background: linear-gradient(135deg, #51cf66 0%, #40c057 100%);
}

/* Warning Modal Specific Styles */
.warning-modal .custom-modal-header {
    background: linear-gradient(135deg, #ffd43b 0%, #fab005 100%);
}

/* Input Modal Styles */
.custom-modal-input {
    width: 100%;
    padding: 15px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-size: 1rem;
    margin-bottom: 20px;
    transition: border-color 0.3s ease;
}

.custom-modal-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Animation for modal entrance */
@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.8) translateY(50px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

@keyframes modalSlideOut {
    from {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
    to {
        opacity: 0;
        transform: scale(0.8) translateY(50px);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .custom-modal {
        width: 95%;
        margin: 20px;
    }
    
    .custom-modal-header {
        padding: 20px;
    }
    
    .custom-modal-body {
        padding: 20px;
    }
    
    .custom-modal-buttons {
        flex-direction: column;
    }
    
    .custom-modal-btn {
        width: 100%;
    }
}
